{"name": "art-design-pro", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open", "build": "vite build", "serve": "vite preview", "lint": "eslint", "fix": "eslint --fix", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "prepare": "husky", "commit": "git-cz", "clean:dev": "tsx scripts/clean-dev.ts"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts,mjs,mts,tsx}": ["eslint --fix", "prettier --write"], "*.{cjs,json,jsonc}": ["prettier --write"], "*.vue": ["eslint --fix", "stylelint --fix --allow-empty-input", "prettier --write"], "*.{html,htm}": ["prettier --write"], "*.{scss,css,less}": ["stylelint --fix --allow-empty-input", "prettier --write"], "*.{md,mdx}": ["prettier --write"], "*.{yaml,yml}": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vue/reactivity": "^3.4.35", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^11.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "next", "axios": "^1.7.7", "bpmn-js": "^17.11.1", "bpmn-js-properties-panel": "^0.44.0", "bpmn-js-token-simulation": "^0.35.1", "camunda-bpmn-moddle": "^7.0.1", "codemirror": "^6.0.1", "codemirror-editor-vue3": "^2.8.0", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "diagram-js": "^14.11.3", "file-saver": "^2.0.5", "highlight.js": "^11.10.0", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "md-editor-v3": "^4.17.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "qrcode": "^1.5.4", "qrcode.vue": "^3.6.0", "quill-image-resize": "^3.0.9", "socket.io-client": "^4.8.1", "unplugin-auto-import": "^0.18.2", "unplugin-vue-components": "^0.27.4", "unplugin-vue-setup-extend-plus": "^1.0.1", "vue": "^3.5.13", "vue-cropper": "^1.1.4", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^9.14.0", "vue-router": "^4.4.2", "vue3-count-to": "^1.1.2", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "xgplayer": "^3.0.20", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.4.1", "@commitlint/config-conventional": "^19.4.1", "@eslint/js": "^9.9.1", "@types/node": "^22.1.0", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/compiler-sfc": "^3.0.5", "autoprefixer": "^10.4.20", "code-inspector-plugin": "^0.20.10", "commitizen": "^4.3.0", "cz-git": "^1.11.1", "eslint": "^9.9.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "fast-glob": "^3.3.2", "globals": "^15.9.0", "husky": "^9.1.5", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "postcss": "^8.4.47", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.81.0", "stylelint": "^16.20.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "tailwindcss": "^3.4.12", "terser": "^5.36.0", "tsx": "^4.20.3", "typescript": "~5.6.3", "typescript-eslint": "^8.9.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^6.1.0", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.6", "vue-demi": "^0.14.9", "vue-img-cutter": "^3.0.5", "vue-tsc": "~2.1.6"}}