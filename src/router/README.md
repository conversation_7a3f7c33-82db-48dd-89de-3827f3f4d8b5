# Router 文件夹结构说明

## 📁 文件夹结构

```
src/router/
├── guards/                 # 路由守卫
│   ├── index.ts           # 守卫统一入口
│   ├── beforeEach.ts      # 前置守卫（包含完整权限逻辑）
│   └── afterEach.ts       # 后置守卫
├── routes/                # 路由配置
│   ├── asyncRoutes.ts     # 异步路由
│   └── staticRoutes.ts    # 静态路由
├── utils/                 # 路由工具函数
│   ├── menuToRouter.ts    # 菜单转路由
│   ├── registerRoutes.ts  # 路由注册
│   └── utils.ts           # 通用工具
├── index.ts               # 路由主入口（已更新）
└── routesAlias.ts         # 路由别名
```

## 🔧 主要改进

### 1. **权限控制集中化**
- 将原 `src/permission.ts` 的所有逻辑整合到 `guards/beforeEach.ts` 中
- 保持功能完整性的同时，结构更加清晰

### 2. **守卫统一管理**
- `guards/index.ts` - 所有守卫的统一入口
- `guards/beforeEach.ts` - 包含完整的权限控制逻辑
- `guards/afterEach.ts` - 后置处理和错误处理

## 📋 文件功能说明

### `guards/beforeEach.ts` - 核心权限控制文件
包含以下功能：
- **权限检查** - `checkPermission()`
- **白名单验证** - `isInWhiteList()`
- **用户认证处理** - `handleAuthenticatedUser()`
- **未登录处理** - `handleUnauthenticatedUser()`
- **动态路由生成** - `generateAndAddRoutes()`
- **页面标题设置** - `setPageTitleAndTab()`
- **进度条控制** - NProgress 配置和管理

### `guards/afterEach.ts`
- 进度条关闭
- 路由导航日志
- 错误处理和上报

### `guards/index.ts`
- 统一设置所有路由守卫
- 导出守卫相关函数

## 🎯 使用方式

路由守卫现在通过 `router/index.ts` 中的 `setupRouterGuards()` 自动设置，无需手动引入。

## 🔄 迁移说明

原 `src/permission.ts` 文件的内容已完全迁移到 `guards/beforeEach.ts` 中：
- ✅ **权限验证逻辑**
- ✅ **路由导航控制**
- ✅ **动态路由生成**
- ✅ **页面标题设置**
- ✅ **进度条管理**
- ✅ **错误处理**

这样的结构更加简洁，所有权限相关的逻辑都集中在一个文件中，便于理解和维护。
