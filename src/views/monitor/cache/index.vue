<template>
  <div class="">
    <el-row>
      <el-col :span="24" class="card-box">
        <el-card>
          <template #header>
            <Monitor style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('information.basic') }}</span>
          </template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%">
              <tbody>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('version.redis') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.redis_version }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('mode.operating') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.redis_mode == 'standalone' ? '单机' : '集群' }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('port') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.tcp_port }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('clients.number') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.connected_clients }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('time.running.days') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.uptime_in_days }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('memory.using') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.used_memory_human }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('cpu.using') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('configuration.memory') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.maxmemory_human }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('aof.enabled') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.aof_enabled == '0' ? '否' : '是' }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('rdb.success') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.rdb_last_bgsave_status }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('key.quantity') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.dbSize" class="cell">{{ cache.dbSize }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('network.ingress.egress') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.instantaneous_input_kbps }}kps/{{
                        cache.info.instantaneous_output_kbps
                      }}kps
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12" class="card-box">
        <el-card>
          <template #header>
            <PieChart style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('statistics.command') }}</span>
          </template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <div ref="commandstats" style="height: 420px" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="12" class="card-box">
        <el-card>
          <template #header>
            <Odometer style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('memory.info') }}</span>
          </template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <div ref="usedmemory" style="height: 420px" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script name="Cache" setup>
  import { getCache } from '@/api/monitor/cache'
  import * as echarts from 'echarts'
  import { getCurrentInstance } from 'vue'

  const { proxy } = getCurrentInstance()

  const cache = ref([])
  const commandstats = ref(null)
  const usedmemory = ref(null)

  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    proxy.$modal.loading(proxy.$t('data.monitoring.cache.loading'))
    getCache().then((response) => {
      proxy.$modal.closeLoading()
      cache.value = response.data

      const commandstatsIntance = echarts.init(commandstats.value, 'macarons')
      commandstatsIntance.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        series: [
          {
            name: proxy.$t('command'),
            type: 'pie',
            roseType: 'radius',
            radius: [15, 95],
            center: ['50%', '38%'],
            data: response.data.commandStats,
            animationEasing: 'cubicInOut',
            animationDuration: 1000
          }
        ]
      })
      const usedmemoryInstance = echarts.init(usedmemory.value, 'macarons')
      usedmemoryInstance.setOption({
        tooltip: {
          formatter: '{b} <br/>{a} : ' + cache.value.info.used_memory_human
        },
        series: [
          {
            name: proxy.$t('peak'),
            type: 'gauge',
            min: 0,
            max: 1000,
            detail: {
              formatter: cache.value.info.used_memory_human
            },
            data: [
              {
                value: parseFloat(cache.value.info.used_memory_human),
                name: proxy.$t('consumption.memory')
              }
            ]
          }
        ]
      })
      window.addEventListener('resize', () => {
        commandstatsIntance.resize()
        usedmemoryInstance.resize()
      })
    })
  }

  getList()
</script>
