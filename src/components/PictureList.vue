<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    class="big-dialog"
    :title="title"
    width="1100px"
    destroy-on-close
    :before-close="
      (fn) => {
        visible = false
        fn()
      }
    "
    @open="open"
  >
    <div class="big-layout-container">
      <!-- 左侧树形菜单 -->
      <div class="big-left-tree">
        <!-- 使用默认树形样式 -->
        <el-tree
          ref="treeRef"
          :current-node-key="0"
          :data="treeData"
          :default-expanded-keys="[0]"
          :expand-on-click-node="false"
          :indent="16"
          :props="defaultProps"
          class="big-tree"
          default-expand-all
          highlight-current
          node-key="id"
          @node-click="handleNodeClick"
        >
          <template #default="{ node }">
            <div class="big-tree-node">
              <img alt="folder" class="big-tree-icon" src="@/assets/img/file.jpg" />
              <span>{{ node.label }}</span>
              <el-dropdown
                class="node-dropdown"
                trigger="click"
                @command="(command) => handleDropdownCommand(command, node.data)"
                @click.stop
              >
                <el-icon class="more-icon" @click.stop>
                  <MoreFilled />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu @click.stop>
                    <el-dropdown-item command="add">新建分类</el-dropdown-item>
                    <el-dropdown-item command="edit">编辑分类</el-dropdown-item>
                    <el-dropdown-item command="delete" type="danger">删除分类</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 右侧内容区域 -->
      <div class="big-right-content">
        <!-- 工具栏 -->
        <div class="big-toolbar">
          <div class="big-toolbar-left">
            <el-button
              :disabled="selectedImages.length === 0"
              icon="Plus"
              plain
              type="primary"
              @click="handleUseSelected"
              >使用选中图片
            </el-button>

            <el-button type="primary" @click="showUploadDialog">上传图片</el-button>
            <el-button
              :disabled="selectedImages.length === 0"
              icon="Delete"
              plain
              type="danger"
              @click="handleDeleteImages"
              >删除图片
            </el-button>

            <category-cascader
              v-model="targetCategoryId"
              :include-all="true"
              clearable
              filterable
              placeholder="图片移动至"
              :type="categoryType"
              @change="handleMoveCategory"
            />
          </div>

          <div class="big-search">
            <el-input
              v-model="queryParams.originalName"
              :suffix-icon="Search"
              clearable
              placeholder="请输入图片名"
              @clear="handleSearch"
              @keyup.enter="handleSearch"
            />
          </div>
        </div>

        <!-- 图片内容区域 -->
        <div class="big-content" ref="containerRef" v-if="visible">
          <el-empty v-if="!hasImages" class="big-empty" description="图片库为空" />
          <div v-else>
            <div
              class="image-grid"
              :style="{
                height: `${Math.ceil(listData.length / columnsPerRow) * (itemSize + gap) + gap * 2}px`,
                position: 'relative',
                width: '100%'
              }"
            >
              <div
                v-for="item in getVisibleItems"
                :key="item.ossId"
                :style="getItemStyle(item)"
                :class="{ 'is-selected': isImageSelected(item) }"
                class="image-item"
                @click="handleImageSelect(item)"
                v-memo="[item.ossId, isImageSelected(item)]"
              >
                <div class="image-wrapper">
                  <el-image
                    :src="item.url"
                    :preview-src-list="previewSrcList"
                    :initial-index="getPreviewIndex(item)"
                    loading="lazy"
                    fit="cover"
                    @click.stop
                    preview-teleported
                  />
                </div>
                <div class="preview-button" @click.stop="handlePreview(item)">
                  <el-icon>
                    <ZoomIn />
                  </el-icon>
                </div>
                <div class="copy-button" @click.stop="handleCopyUrl(item)">
                  <el-icon>
                    <CopyDocument />
                  </el-icon>
                </div>
                <div class="image-title" @click.stop>{{ item.originalName }}</div>
                <div v-if="isImageSelected(item)" class="selected-badge">
                  {{ selectedImageMap.get(item.ossId)?.index || '' }}
                </div>
              </div>
            </div>
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="queryParams.pageNum"
                v-model:page-size="queryParams.pageSize"
                background
                :page-sizes="[12, 24, 36, 48]"
                :total="total"
                layout="total, prev, pager, next"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <upload-dialog
      ref="uploadDialogRef"
      :category-data="treeData"
      :category-id="currentCategoryId"
      @upload-success="list"
    />

    <!-- 添加新建分类对话框 -->
    <el-dialog
      v-model="addCategoryVisible"
      :close-on-click-modal="false"
      :title="dialogTitle"
      width="500px"
    >
      <el-form ref="addFormRef" :model="addForm" :rules="rules" label-width="100px">
        <el-form-item label="上级分类" prop="parentId">
          <el-select v-model="addForm.parentId" placeholder="请选择上级分类">
            <el-option :value="0" label="顶级分类" />
            <el-option
              v-for="item in treeData.filter((el) => el.id !== 0 && el.id !== addForm.id)"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="addForm.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addCategoryVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAdd">确定</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
  import {
    addCategory,
    delCategory,
    listCategory,
    updateCategory
  } from '@/api/system/category/Category'
  import { delOss, moveTag, pageList } from '@/api/system/oss/Oss'
  import { CopyDocument, MoreFilled, Search, ZoomIn } from '@element-plus/icons-vue'
  import { getCurrentInstance } from 'vue'
  import UploadDialog from './UploadDialog.vue'

  const { proxy } = getCurrentInstance()
  const treeRef = ref(null)
  const hasImages = ref(false)
  const listData = ref([])
  const total = ref(0)

  const treeData = ref([])

  const defaultProps = {
    children: 'children',
    label: 'categoryName'
  }

  const isInitialized = ref(false)
  const renderTimer = ref(null)

  const open = async () => {
    // 重置所有状态
    resetState()

    // 获取分类数据
    try {
      const { data } = await listCategory({ type: props.categoryType })
      treeData.value = [
        {
          id: '0',
          categoryName: '全部图片'
        }
      ].concat(data)

      nextTick(() => {
        treeRef.value?.setCurrentKey(0)
      })
    } catch (error) {
      console.error('获取分类数据失败:', error)
    }

    // 延迟加载图片列表
    renderTimer.value = setTimeout(() => {
      list()
      isInitialized.value = true
    }, 100)
  }

  const resetState = () => {
    hasImages.value = false
    selectedImageMap.value.clear()
    treeData.value = []
    currentCategoryId.value = '0'
    queryParams.value.pageNum = 1
    queryParams.value.categoryId = ''
    queryParams.value.originalName = ''
    isInitialized.value = false
    // 清理动画帧
    if (rafId) {
      cancelAnimationFrame(rafId)
      rafId = null
    }
  }

  const queryParams = ref({
    pageNum: 1,
    pageSize: 18,
    categoryId: '',
    originalName: '',
    fileType: 'image'
  })

  const list = () => {
    pageList(queryParams.value).then(({ data }) => {
      listData.value = data.records
      total.value = data.totalRow
      hasImages.value = data.totalRow > 0
    })
  }

  const handleNodeClick = (data) => {
    queryParams.value.categoryId = data.id === '0' ? '' : data.id.toString()
    currentCategoryId.value = data.id === '0' ? '' : data.id.toString()
    list()
  }

  const handleSearch = () => {
    queryParams.value.pageNum = 1
    list()
  }

  const handleSizeChange = (val) => {
    queryParams.value.pageSize = val
    list()
  }

  const handleCurrentChange = (val) => {
    queryParams.value.pageNum = val
    list()
  }

  // 优化：使用 shallowRef 避免深度响应式，提升性能
  const previewSrcList = computed(() => {
    return listData.value.map((item) => item.url)
  })

  // 优化：使用缓存的索引映射
  const getPreviewIndex = (item) => {
    return imageIndexMap.value.get(item.ossId) || 0
  }

  // 优化：简化选中状态管理，只使用一个 Map
  const selectedImageMap = ref(new Map())

  // 优化：使用计算属性自动维护选中列表
  const selectedImages = computed(() => Array.from(selectedImageMap.value.values()))

  // 优化：直接从 Map 检查选中状态
  const isImageSelected = (item) => selectedImageMap.value.has(item.ossId)

  // 优化：简化选中逻辑，减少重复计算
  const handleImageSelect = (item) => {
    const ossId = item.ossId
    if (selectedImageMap.value.has(ossId)) {
      selectedImageMap.value.delete(ossId)
      // 重新计算剩余选中项的序号
      let index = 1
      selectedImageMap.value.forEach((value) => {
        value.index = index++
      })
    } else {
      if (selectedImageMap.value.size >= props.maxSelect) {
        proxy.$msg.warning(`最多只能选择 ${props.maxSelect} 张图片`)
        return
      }
      selectedImageMap.value.set(ossId, {
        ossId,
        url: item.url,
        originalName: item.originalName,
        index: selectedImageMap.value.size + 1
      })
    }
    // 触发响应式更新
    selectedImageMap.value = new Map(selectedImageMap.value)
  }

  // 修改预览处理方法
  const handlePreview = (item) => {
    const index = listData.value.indexOf(item)
    // 只选择图片列表容器内的图片元素
    const imageRefs = document.querySelector('.image-grid')?.querySelectorAll('.el-image img')
    imageRefs?.[index]?.click()
  }

  // 添加：定��� props 和 emits
  const props = defineProps({
    show: {
      type: Boolean,
      default: false
    },
    maxSelect: {
      type: Number,
      default: 1 // 默认只能选择一张图片
    },
    title: {
      type: String,
      default: '上传商品图'
    },
    categoryType: {
      type: String,
      default: 'upload_img'
    }
  })

  const emit = defineEmits(['select', 'update:show'])
  let visible = computed({
    get: () => props.show,
    set: (val) => {
      emit('update:show', val)
    }
  })
  // 添加：处理使用选中图片的方法
  const handleUseSelected = () => {
    if (selectedImageMap.value.size === 0) {
      ElMessage.warning('请先选择图片')
      return
    }
    // 发送选中的图片数据到父组件，按选中顺序排序
    const sortedImages = Array.from(selectedImageMap.value.values()).sort(
      (a, b) => a.index - b.index
    )
    emit('select', sortedImages)

    // 清空所有选中状态
    selectedImageMap.value.clear()

    // 关闭对话框
    visible.value = false
  }

  // 修改 watch visible 的处理，确保完全清空状态
  watch(visible, (newVal) => {
    if (!newVal) {
      // 关闭对话框时清空所有状态
      selectedImageMap.value.clear()
      // selectedCategoryId.value = ''
      currentCategoryId.value = '0'
      queryParams.value.pageNum = 1
      queryParams.value.categoryId = ''
      queryParams.value.originalName = ''
    } else {
      // 打开对话框时重置状态
      selectedImageMap.value.clear()
    }
  })

  // 添加上传对话框的引用
  const uploadDialogRef = ref(null)

  // 添加当前选中的分类ID
  const currentCategoryId = ref('0')

  // 修改显示上传对话框的方
  const showUploadDialog = () => {
    if (!currentCategoryId.value && queryParams.value.categoryId) {
      currentCategoryId.value = queryParams.value.categoryId
    }
    if (currentCategoryId.value === '') {
      currentCategoryId.value = '0'
    }
    // 确保有分类数据后再显示对话框
    if (treeData.value.length > 0) {
      uploadDialogRef.value?.show()
    } else {
      // 如果没有分类数据，先获取分类数据
      listCategory({ type: props.categoryType }).then(({ data }) => {
        treeData.value = [
          {
            id: '0',
            categoryName: '全部图片'
          }
        ].concat(data)
        uploadDialogRef.value?.show()
      })
    }
  }

  // 表单相关数据
  const addCategoryVisible = ref(false)
  const addFormRef = ref(null)
  const addForm = ref({
    parentId: 0,
    categoryName: '',
    type: props.categoryType,
    id: undefined
  })

  // 表单标题
  const dialogTitle = computed(() => {
    return addForm.value.id ? '编辑分类' : '新建分类'
  })

  // 处理新建分类
  const handleAddCategory = (parentNode) => {
    addForm.value = {
      parentId: parentNode?.id || 0,
      categoryName: '',
      type: props.categoryType,
      id: undefined
    }
    addCategoryVisible.value = true
  }

  // 处理编辑分类
  const handleEditCategory = (node) => {
    // 防止编辑"全部图片"分类
    if (node.id === 0) {
      proxy.$msg.warning('不能编辑系统分类')
      return
    }

    addForm.value = {
      id: node.id,
      parentId: node.parentId || '0',
      categoryName: node.categoryName,
      type: props.categoryType
    }
    addCategoryVisible.value = true
  }

  // 处理删除分类
  const handledelCategory = async (node) => {
    // 防止删除"全部图片"分类
    if (node.id === '0') {
      proxy.$msg.warning('不能删除系统分类')
      return
    }

    if (node.children && node.children.length > 0) {
      proxy.$msg.error('该分类下存在子分类，无法删除')
      return
    }

    try {
      await proxy.$msg.confirm('确定要删除该分类吗？')
      await delCategory(node.id)
      proxy.$msg.success('删除成功')
      refreshTreeData()
    } catch (error) {
      console.error('删除分类失败:', error)
    }
  }

  // 刷新树形数据
  const refreshTreeData = async () => {
    try {
      const { data } = await listCategory({ type: props.categoryType })
      treeData.value = [
        {
          id: '0',
          categoryName: '全部图片'
        }
      ].concat(data)

      // 保持当前选中状态
      nextTick(() => {
        if (currentCategoryId.value) {
          treeRef.value?.setCurrentKey(currentCategoryId.value)
        } else {
          treeRef.value?.setCurrentKey('0')
        }
      })
    } catch (error) {
      console.error('刷新分类数据失败:', error)
    }
  }

  // 提交表单
  const submitAdd = () => {
    addFormRef.value?.validate(async (valid) => {
      if (valid) {
        try {
          // 处理ancestors
          if (addForm.value.parentId !== 0) {
            const parentNode = treeData.value.find(
              (node) => node.id.toString() === addForm.value.parentId.toString()
            )
            if (!parentNode) {
              proxy.$msg.warning('父级分类不存在')
              return
            }
            addForm.value.ancestors = parentNode.ancestors
              ? `${parentNode.ancestors},${parentNode.id.toString()}`
              : parentNode.id.toString()
          } else {
            addForm.value.ancestors = '0'
          }

          // ��保ID是字符串形式
          if (addForm.value.id) {
            addForm.value.id = addForm.value.id.toString()
          }
          addForm.value.parentId = addForm.value.parentId.toString()

          if (addForm.value.id) {
            await updateCategory(addForm.value)
            proxy.$msg.success('修改成功')
          } else {
            await addCategory(addForm.value)
            proxy.$msg.success('新增成功')
          }

          addCategoryVisible.value = false
          addFormRef.value?.resetFields()
          refreshTreeData()
        } catch (error) {
          console.error('保存分类失败:', error)
          proxy.$msg.msgError('保存失败：' + error.message)
        }
      }
    })
  }

  // 监听对话框关闭，重置表单
  watch(addCategoryVisible, (val) => {
    if (!val) {
      addFormRef.value?.resetFields()
    }
  })

  // 添加移���对话框相关的响应式变量
  const targetCategoryId = ref(null)

  // 格式化分类数据的递归函数
  const formatCategoryData = (category) => {
    const result = {
      id: category.id,
      categoryName: category.categoryName,
      disabled: category.id === currentCategoryId.value
    }

    if (category.children && category.children.length > 0) {
      result.children = category.children.map((child) => formatCategoryData(child))
    }

    return result
  }

  // 修改 handleMoveCategory 方法
  const handleMoveCategory = async (value) => {
    if (!value) return

    if (selectedImageMap.value.size === 0) {
      proxy.$msg.error('请先选择要移动的图片')
      targetCategoryId.value = null
      return
    }

    try {
      await moveTag({
        ossIds: Array.from(selectedImageMap.value.keys()).map((id) => id.toString()),
        categoryId: value.toString()
      })

      proxy.$msg.success('移动成功')
      targetCategoryId.value = null
      selectedImageMap.value.clear()
      selectedImages.value = []
      list()
    } catch (error) {
      console.error('移动失败:', error)
      proxy.$msg.error('移动失败')
    }
  }

  // 表单验证规则
  const rules = {
    categoryName: [
      { required: true, message: '请输入分类名称', trigger: 'blur' },
      { min: 1, max: 50, message: '分类名称长度应在 1 到 50 个字符之间', trigger: 'blur' }
    ],
    parentId: [{ required: true, message: '请选择上级分类', trigger: 'change' }]
  }

  // 修改下拉菜单点击事件处理
  const handleDropdownCommand = (command, node) => {
    switch (command) {
      case 'add':
        handleAddCategory(node)
        break
      case 'edit':
        handleEditCategory(node)
        break
      case 'delete':
        handledelCategory(node)
        break
    }
  }

  // 修改 handleDeleteImages 方法
  const handleDeleteImages = async () => {
    if (selectedImageMap.value.size === 0) {
      proxy.$msg.warning('请先选择要删除的图片')
      return
    }

    try {
      await proxy.$modal.confirm('确定要删除选中的图片吗？')
      await delOss(Array.from(selectedImageMap.value.keys()).map((id) => id.toString()))
      proxy.$msg.success('删除成功')
      selectedImageMap.value.clear()
      selectedImages.value = []
      list()
    } catch (error) {
      console.error('删除图片失败:', error)
      if (error !== 'cancel') {
        proxy.$msg.error('删除失败：' + error.message)
      }
    }
  }

  // 修改图片列表的渲染，使用虚拟列表
  const containerRef = ref(null)
  const itemSize = 120 // 图片项的大小
  const gap = 16 // 间距
  const buffer = 5 // 缓冲区大小

  const visibleRange = reactive({
    start: 0,
    end: 0
  })

  const updateVisibleRange = () => {
    if (!containerRef.value) return

    const container = containerRef.value
    const scrollTop = container.scrollTop
    const containerHeight = container.clientHeight
    const containerWidth = container.clientWidth

    // 计算每行能显示多少列
    const columnsPerRow = Math.floor((containerWidth - gap) / (itemSize + gap))
    const rowHeight = itemSize + gap

    // 计算当前可见区域的行范围
    const startRow = Math.floor(scrollTop / rowHeight)
    const visibleRows = Math.ceil(containerHeight / rowHeight)
    const bufferRows = buffer // 上下各多渲染几行作为缓冲

    // 计算实际需要渲染的行范围
    const startIndex = Math.max(0, (startRow - bufferRows) * columnsPerRow)
    const endIndex = Math.min(
      listData.value.length,
      (startRow + visibleRows + bufferRows) * columnsPerRow
    )

    visibleRange.start = startIndex
    visibleRange.end = endIndex
  }

  // 优化：真正实现虚拟列表
  const visibleItems = computed(() => {
    if (!isInitialized.value || !listData.value.length) return []
    return listData.value.slice(visibleRange.start, visibleRange.end)
  })

  // 优化：使用 RAF 替代防抖，更流畅
  let rafId = null
  const updateVisibleRangeRAF = () => {
    if (rafId) return
    rafId = requestAnimationFrame(() => {
      updateVisibleRange()
      rafId = null
    })
  }

  // 优化：单例 ResizeObserver
  let resizeObserver = null

  const initializeObservers = () => {
    if (!containerRef.value) return

    // 优化：使用 RAF 的滚动处理
    const handleScroll = () => {
      if (isInitialized.value) {
        updateVisibleRangeRAF()
      }
    }

    containerRef.value.addEventListener('scroll', handleScroll, { passive: true })

    // 优化：单例 ResizeObserver
    if (!resizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        if (isInitialized.value) {
          updateVisibleRangeRAF()
        }
      })
    }

    resizeObserver.observe(containerRef.value)

    return () => {
      containerRef.value?.removeEventListener('scroll', handleScroll)
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }
    }
  }

  // 添加计算位置的方法
  const columnsPerRow = computed(() => {
    if (!containerRef.value) return 1
    return Math.floor((containerRef.value.clientWidth - gap) / (itemSize + gap))
  })



  // 优化：使用缓存的索引，避免 indexOf
  const getItemStyle = (item) => {
    const index = imageIndexMap.value.get(item.ossId) || 0
    const column = index % columnsPerRow.value
    const row = Math.floor(index / columnsPerRow.value)

    return {
      position: 'absolute',
      transform: `translate3d(${column * (itemSize + gap) + gap}px, ${row * (itemSize + gap) + gap}px, 0)`,
      width: `${itemSize}px`,
      height: `${itemSize}px`,
      willChange: 'transform'
    }
  }

  // 优化：使用真正的虚拟列表数据
  const getVisibleItems = computed(() => visibleItems.value)

  // 优化：缓存图片索引映射，避免频繁 indexOf
  const imageIndexMap = computed(() => {
    const map = new Map()
    listData.value.forEach((item, index) => {
      map.set(item.ossId, index)
    })
    return map
  })

  // 优化组件生命周期
  let cleanup = null

  onMounted(() => {
    cleanup = initializeObservers()
  })

  onBeforeUnmount(() => {
    if (cleanup) cleanup()
    if (renderTimer.value) {
      clearTimeout(renderTimer.value)
    }
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }
    resetState()
  })

  // 优化监听器
  watch(
    visible,
    (newVal) => {
      if (!newVal) {
        resetState()
      }
    },
    { immediate: true }
  )

  // 添加复制URL的处理方法
  const handleCopyUrl = async (item) => {
    try {
      await navigator.clipboard.writeText(item.url)
      proxy.$msg.success('复制成功')
    } catch (err) {
      proxy.$msg.error('复制失败')
    }
  }
</script>

<style scoped>
  .big-dialog :deep(.el-dialog) {
    height: 80vh !important;
    margin-top: 10vh;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog) {
    height: 80vh !important;
    margin-top: 10vh !important;
  }

  .big-dialog :deep(.el-dialog__body) {
    flex: 1;
    overflow: hidden;
    padding: 0;
  }

  .big-layout-container {
    height: 100%;
    display: flex;
  }

  .big-left-tree {
    width: 200px;
    border-right: 1px solid var(--el-border-color);
    padding: 4px 10px 4px 4px;
    overflow-y: auto;
  }

  .big-right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .big-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    height: 52px;
    flex-shrink: 0;
  }

  .big-toolbar-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .big-search {
    width: 200px;
  }

  .big-content {
    flex: 1;
    padding: 4px;
    overflow-y: auto;
    height: 0;
  }

  .big-empty {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
  }

  .big-tree :deep(.el-tree) {
    background: transparent;
  }

  .big-tree :deep(.el-tree-node) {
    position: relative;
    padding-left: 0;
  }

  .big-tree :deep(.el-tree-node__content) {
    height: auto;
    padding: 0;
  }

  .big-tree :deep(.el-tree-node__children) {
    padding-left: 16px;
  }

  .big-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: transparent;
    color: var(--el-color-primary);
  }

  .big-tree :deep(.el-tree-node__content:hover) {
    background-color: transparent;
  }

  .big-tree :deep(.el-tree-node.is-current > .el-tree-node__content:hover) {
    color: var(--el-color-primary);
  }

  .big-tree :deep(.el-tree-node__expand-icon) {
    padding: 8px;
  }

  .big-tree :deep(.el-tree-node.is-expanded > .el-tree-node__children) {
    display: block;
  }

  .big-toolbar :deep(.el-button + .el-dropdown) {
    margin-left: 3px;
  }

  .big-tree-node {
    display: flex;
    align-items: center;
    gap: 4px;
    width: 100%;
    position: relative;
    user-select: none;
  }

  .big-tree-icon {
    width: 12px;
    height: 12px;
    object-fit: contain;
  }

  .big-category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .big-category-left {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .big-category-more {
    font-size: 16px;
    cursor: pointer;
    color: var(--el-text-color-secondary);
    padding: 4px;
  }

  .big-category-card {
    background-color: var(--el-fill-color-light);
    border-radius: 8px;
    padding: 8px 12px;
    margin: 4px 0;
    width: 100%;
  }

  .big-category-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .big-category-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .big-folder-icon {
    font-size: 16px;
    color: #ffd700;
  }

  .big-more-icon {
    font-size: 16px;
    color: var(--el-text-color-secondary);
    cursor: pointer;
    user-select: none;
  }

  /* 选中状态样式 */
  .big-tree :deep(.el-tree-node.is-current > .el-tree-node__content .big-category-card) {
    background-color: var(--el-color-primary-light-9);
  }

  .image-grid {
    padding: 0;
    margin: 0;
    position: relative;
    width: 100%;
    min-height: 200px;
    /* 添加最小高度 */
  }

  .image-item {
    position: absolute;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    cursor: pointer;
    border: 2px solid transparent;
    will-change: transform;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .image-item:hover {
    transform: translate3d(0, -2px, 0);
  }

  .image-item.is-selected {
    border: 2px solid var(--el-color-primary);
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
    transform: translate3d(0, -2px, 0);
  }

  .image-item.is-selected:hover {
    transform: translate3d(0, -3px, 0);
    box-shadow:
      0 0 0 4px rgba(64, 158, 255, 0.2),
      0 8px 16px rgba(0, 0, 0, 0.1);
  }

  .image-item .el-image {
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .image-item .el-image :deep(img) {
    pointer-events: none;
  }

  .image-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 4px 8px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    z-index: 2;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    padding: 16px;
  }

  .selected-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background-color: var(--el-color-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 3;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: badge-pop 0.3s ease-out;
    transform: translate3d(0, 0, 0);
    will-change: transform;
  }

  @keyframes badge-pop {
    0% {
      transform: scale(0);
    }

    70% {
      transform: scale(1.2);
    }

    100% {
      transform: scale(1);
    }
  }

  .preview-button,
  .copy-button {
    position: absolute;
    top: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 2;
  }

  .preview-button {
    left: calc(50% - 25px);
    transform: translate(-50%, -50%);
  }

  .copy-button {
    left: calc(50% + 25px);
    transform: translate(-50%, -50%);
  }

  .preview-button:hover,
  .copy-button:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .image-item:hover .preview-button,
  .image-item:hover .copy-button {
    opacity: 1;
  }

  .image-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .image-wrapper :deep(.el-image) {
    width: 100%;
    height: 100%;
    display: block;
  }

  .image-wrapper :deep(.el-image img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .node-dropdown {
    margin-left: auto;
    padding: 4px;
  }

  .more-icon {
    font-size: 16px;
    color: var(--el-text-color-secondary);
    cursor: pointer;
    user-select: none;
  }

  .el-dropdown-menu :deep(.el-radio) {
    width: 100%;
    margin-right: 0;
    padding: 5px 0;
  }

  .el-dropdown-menu :deep(.el-radio__label) {
    padding-left: 5px;
  }

  .category-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 0;
    width: 100%;
    transition: padding-left 0.3s;
  }

  .category-icon {
    width: 14px;
    height: 14px;
    object-fit: contain;
  }

  .category-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 修改下拉菜单项的样式 */
  :deep(.el-dropdown-menu__item) {
    padding: 0 16px;
  }

  :deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
    background-color: var(--el-color-primary-light-9);
  }

  :deep(.el-dropdown-menu__item.is-disabled) {
    cursor: not-allowed;
    opacity: 0.6;
  }

  /* 添加简化的级联选择器样式 */
  .move-cascader :deep(.el-input__wrapper) {
    padding: 1px 11px;
  }

  .move-cascader :deep(.el-input__inner) {
    font-size: var(--el-font-size-base);
  }

  :deep(.el-cascader-menu) {
    min-width: 110px;
  }

  /* 添加性能优化相关样式 */
  .image-item {
    /* 添加硬件加速 */
    transform: translate3d(0, 0, 0);
    will-change: transform;
    contain: layout style paint;
  }

  .image-wrapper {
    contain: content;
  }

  /* 添加图片懒加载占位样式 */
  .image-wrapper :deep(.el-image.is-loading) {
    background-color: var(--el-fill-color-lighter);
  }
</style>
